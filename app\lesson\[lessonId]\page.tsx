import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import LessonClient from './lesson-client'
import { getLessonServerSide, getSubjectServerSide } from '@/backend/api/educationAPI'
import { generateLessonMetadata } from '@/lib/seo'

type Props = {
  params: { lessonId: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    const lesson = await getLessonServerSide(params.lessonId)
    if (!lesson) {
      return {
        title: 'الدرس غير موجود - منصة التعليم العربي',
        description: 'لم يتم العثور على الدرس المطلوب',
        robots: { index: false, follow: false }
      }
    }

    const subject = await getSubjectServerSide(lesson.subjectId)
    return generateLessonMetadata(lesson, subject)
  } catch (error) {
    console.error('Error generating lesson metadata:', error)
    return {
      title: 'الدرس - منصة التعليم العربي',
      description: 'استكشف تمارين الدرس التفاعلية',
    }
  }
}

// Loading component
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

export default function LessonPage({ params }: Props) {
  return (
    <Suspense fallback={<PageLoader />}>
      <LessonClient lessonId={params.lessonId} />
    </Suspense>
  )
}

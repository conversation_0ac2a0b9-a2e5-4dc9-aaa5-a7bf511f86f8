'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getLesson, getSubject, getYear, getLevel } from '@/data/educationData';
import SummaryList from '@/components/summary/SummaryList';
import type { Lesson as LessonType } from '@/data/types';
import { useViewerMode } from '@/hooks/use-viewer-mode';

interface SummaryClientProps {
  lessonId: string;
}

const SummaryClient = ({ lessonId }: SummaryClientProps) => {
  const [lesson, setLesson] = useState<LessonType | null>(null);
  const [subjectName, setSubjectName] = useState('');
  const [yearName, setYearName] = useState('');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [levelName, setLevelName] = useState('');
  const [subjectId, setSubjectId] = useState('');
  const [yearId, setYearId] = useState('');
  const [loading, setLoading] = useState(true);
  const { isViewerMode } = useViewerMode();
  const searchParams = useSearchParams();

  // الحصول على رابط العودة من معاملات URL
  const returnTo = searchParams.get('returnTo');

  useEffect(() => {
    const fetchData = async () => {
      if (!lessonId) return;

      setLoading(true);
      console.log("جاري تحميل ملخص الدرس بالمعرف:", lessonId);

      try {
        // Find the lesson data
        const lessonData = await getLesson(lessonId);
        console.log("تم تحميل بيانات ملخص الدرس:", lessonData);

        if (lessonData) {
          setLesson(lessonData);
          console.log("تم تعيين بيانات ملخص الدرس في الحالة:", lessonData);
          console.log("عدد عناصر الملخص:", lessonData.exercises?.length || 0);
          console.log("تفاصيل عناصر الملخص:", lessonData.exercises);

          // Get subject data
          const subject = await getSubject(lessonData.subjectId);
          console.log("تم تحميل بيانات المادة:", subject);

          if (subject) {
            setSubjectName(subject.name);
            setSubjectId(subject.id);

            // Get year data
            const year = await getYear(subject.yearId);
            console.log("تم تحميل بيانات السنة:", year);

            if (year) {
              setYearName(year.name);
              setYearId(year.id);

              // Get level data
              const level = await getLevel(year.levelId);
              console.log("تم تحميل بيانات المستوى:", level);

              if (level) {
                setLevelName(level.name);
              }
            }
          }
        } else {
          console.log("لم يتم العثور على ملخص الدرس");
        }
      } catch (error) {
        console.error("خطأ في تحميل بيانات ملخص الدرس:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [lessonId]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg">جاري تحميل البيانات...</p>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  if (!lesson) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <h1 className="text-3xl font-bold mb-6 text-primary">لم يتم العثور على ملخص الدرس</h1>
          <Link href="/levels" className="text-primary hover:underline">
            العودة إلى المستويات الدراسية
          </Link>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  console.log(`عدد عناصر ملخص الدرس المتاحة في "${lesson.title}": ${lesson.exercises?.length || 0}`);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {!isViewerMode && <Header />}

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">{lesson.title}</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl">
            {lesson.description}
          </p>
        </div>

        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-muted-foreground">
            <Link href="/levels" className="hover:text-primary transition-colors">
              المستويات
            </Link>
            <span>/</span>
            {yearId && (
              <>
                <Link href={`/year/${yearId}`} className="hover:text-primary transition-colors">
                  {yearName}
                </Link>
                <span>/</span>
              </>
            )}
            {subjectId && (
              <>
                <Link href={returnTo || `/subject/${subjectId}`} className="hover:text-primary transition-colors">
                  {subjectName}
                </Link>
                <span>/</span>
              </>
            )}
            <span className="text-foreground">{lesson.title}</span>
          </nav>
        </div>

        {/* Summary Exercises */}
        <SummaryList
          exercises={lesson.exercises}
          lessonTitle={lesson.title}
          subjectName={subjectName}
          yearName={yearName}
          levelName={levelName}
        />
      </div>

      {!isViewerMode && (
        <div className="mt-auto">
          <Footer />
        </div>
      )}
    </div>
  );
};

export default SummaryClient;

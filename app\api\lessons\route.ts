import { NextRequest, NextResponse } from 'next/server';
import { fetchLessonsForSubjectFromSupabase } from '@/backend/utils/supabaseLoader';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const subjectId = searchParams.get('subjectId');

    if (!subjectId) {
      return NextResponse.json(
        { error: 'subjectId is required' },
        { status: 400 }
      );
    }

    // جلب الدروس مباشرة من Supabase لتجنب مشاكل localStorage في بيئة الخادم
    const lessons = await fetchLessonsForSubjectFromSupabase(subjectId);
    
    return NextResponse.json({
      success: true,
      count: lessons.length,
      lessons: lessons.map(lesson => ({
        id: lesson.id,
        title: lesson.title,
        display_order: lesson.display_order,
        content_type: lesson.content_type
      }))
    });
  } catch (error) {
    console.error('Error fetching lessons:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

'use client'

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { ArrowLeft, GraduationCap } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ExamList from '@/components/exam/ExamList';
import { getLesson, getSubject, getYear, getLevel } from '@/data/educationData';
import { isExamLessonAccessible } from '@/utils/examFilter';
import type { Lesson, Subject, Year, Level } from '@/data/types';
import { useViewerMode } from '@/hooks/use-viewer-mode';

interface ExamClientProps {
  lessonId: string;
}

export default function ExamClient({ lessonId }: ExamClientProps) {
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [subject, setSubject] = useState<Subject | null>(null);
  const [year, setYear] = useState<Year | null>(null);
  const [level, setLevel] = useState<Level | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAccessible, setIsAccessible] = useState<boolean>(true);
  const { isViewerMode } = useViewerMode();
  const searchParams = useSearchParams();

  // الحصول على رابط العودة من معاملات URL
  const returnTo = searchParams.get('returnTo');

  useEffect(() => {
    const fetchData = async () => {
      if (!lessonId) return;

      setLoading(true);
      console.log("جاري تحميل الامتحان بالمعرف:", lessonId);

      try {
        // Check if this exam lesson is accessible based on educational level filtering
        const accessible = await isExamLessonAccessible(lessonId, getLesson, getSubject, getYear);
        setIsAccessible(accessible);

        if (!accessible) {
          console.log("الامتحان غير متاح لهذا المستوى التعليمي");
          setLoading(false);
          return;
        }

        // Find the lesson data
        const lessonData = await getLesson(lessonId);
        console.log("تم تحميل بيانات الامتحان:", lessonData);

        if (lessonData) {
          setLesson(lessonData);

          // Get subject data
          const subjectData = await getSubject(lessonData.subjectId);
          if (subjectData) {
            setSubject(subjectData);

            // Get year data
            const yearData = await getYear(subjectData.yearId);
            if (yearData) {
              setYear(yearData);

              // Get level data
              const levelData = await getLevel(yearData.levelId);
              if (levelData) {
                setLevel(levelData);
              }
            }
          }
        } else {
          console.log("لم يتم العثور على الامتحان");
        }
      } catch (error) {
        console.error("خطأ في تحميل بيانات الامتحان:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [lessonId]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg">جاري تحميل البيانات...</p>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  if (!lesson || !isAccessible) {
    return (
      <div className="min-h-screen flex flex-col bg-background">
        {!isViewerMode && <Header />}
        <div className="container mx-auto px-4 py-10 text-center">
          <GraduationCap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">
            {!isAccessible ? 'الامتحان غير متاح' : 'الامتحان غير موجود'}
          </h1>
          <p className="text-muted-foreground mb-6">
            {!isAccessible ?
              'الامتحانات متاحة فقط للسادس ابتدائي، الثالث إعدادي، الأولى باك، والثانية باك' :
              'عذراً، لم يتم العثور على الامتحان المطلوب'
            }
          </p>
          <Link
            href="/levels"
            className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة إلى المراحل الدراسية
          </Link>
        </div>
        {!isViewerMode && (
          <div className="mt-auto">
            <Footer />
          </div>
        )}
      </div>
    );
  }

  console.log(`عدد أسئلة الامتحان المتاحة في "${lesson.title}": ${lesson.exercises?.length || 0}`);

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {!isViewerMode && <Header />}

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-4">{lesson.title}</h1>
          <p className="text-muted-foreground mx-auto max-w-2xl">
            {lesson.description}
          </p>
        </div>

        {/* Breadcrumb */}
        <div className="mb-8">
          <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-muted-foreground">
            <Link href="/levels" className="hover:text-primary transition-colors">
              المستويات
            </Link>
            <span>/</span>
            {year && (
              <>
                <Link href={`/year/${year.id}`} className="hover:text-primary transition-colors">
                  {year.name}
                </Link>
                <span>/</span>
              </>
            )}
            {subject && (
              <>
                <Link href={returnTo || `/subject/${subject.id}`} className="hover:text-primary transition-colors">
                  {subject.name}
                </Link>
                <span>/</span>
              </>
            )}
            <span className="text-foreground">{lesson.title}</span>
          </nav>
        </div>

        {/* Exam Questions */}
        {lesson.exercises.length > 0 ? (
          <ExamList
            exercises={lesson.exercises}
            lessonTitle={lesson.title}
            subjectName={subject?.name}
            yearName={year?.name}
            levelName={level?.name}
          />
        ) : (
          <div className="text-center py-12">
            <GraduationCap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">لا توجد أسئلة امتحان</h3>
            <p className="text-muted-foreground mb-6">
              لم يتم إضافة أسئلة امتحان لهذا الدرس بعد
            </p>
            {subject && (
              <Link
                href={returnTo || `/subject/${subject.id}`}
                className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة إلى المادة
              </Link>
            )}
          </div>
        )}
      </div>

      {!isViewerMode && (
        <div className="mt-auto">
          <Footer />
        </div>
      )}
    </div>
  );
}

@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force CSS loading and ensure styles are applied */
html {
  font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helvetica', sans-serif;
}

/* Arabic text improvements */
.arabic-text {
  font-family: '<PERSON><PERSON><PERSON>', 'Arial', 'Helvetica', sans-serif;
  line-height: 1.8;
  letter-spacing: 0.02em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Better Arabic paragraph styling */
.arabic-paragraph {
  line-height: 2;
  margin-bottom: 1.5rem;
  text-align: right;
  word-spacing: 0.1em;
}

/* Arabic heading improvements */
.arabic-heading {
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 0;
  text-align: right;
  color: hsl(var(--primary));
}

/* Fix for inline arabic heading alignment */
.arabic-heading-inline {
  font-weight: 700;
  line-height: 1;
  margin-bottom: 0;
  text-align: right;
  color: hsl(var(--primary));
  display: inline-block;
  vertical-align: middle;
}

/* RTL-specific improvements */
.rtl-container {
  direction: rtl;
  text-align: right;
}

/* RTL-aware flexbox utilities */
.rtl-flex-row-reverse {
  flex-direction: row-reverse;
}

.rtl-space-x-reverse > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* RTL-aware positioning */
.rtl-left-auto {
  left: auto;
  right: 0;
}

.rtl-right-auto {
  right: auto;
  left: 0;
}

/* RTL-aware margins and padding */
.rtl-ml-auto {
  margin-left: auto;
  margin-right: 0;
}

.rtl-mr-auto {
  margin-right: auto;
  margin-left: 0;
}

.rtl-pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

.rtl-pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* RTL-aware borders */
.rtl-border-l {
  border-left: none;
  border-right: 1px solid hsl(var(--border));
}

.rtl-border-r {
  border-right: none;
  border-left: 1px solid hsl(var(--border));
}

/* Improved text contrast for better readability */
.text-enhanced {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Better card styling for Arabic content */
.arabic-card {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
}

.arabic-card:hover {
  border-color: hsl(var(--primary) / 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Disabled solution button styling */
.solution-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  position: relative;
}

.solution-button-disabled:hover {
  background-color: transparent;
  border-color: hsl(var(--muted-foreground) / 0.3);
  color: hsl(var(--muted-foreground));
}

/* Enhanced disabled button styling */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

button:disabled:hover {
  background-color: transparent;
  border-color: hsl(var(--muted-foreground) / 0.3);
  color: hsl(var(--muted-foreground));
}



@layer base {
  :root {
    /* نظام الألوان التعليمي - Educational Color System */
    --background: 120 25% 97%;        /* 🤍 خلفية خضراء فاتحة مريحة للعين */
    --foreground: 160 20% 15%;        /* نص أخضر داكن للقراءة الواضحة */

    --card: 0 0% 100%;                /* ⚪ بطاقات بيضاء نقية للوضوح */
    --card-foreground: 160 20% 15%;   /* نص داكن على البطاقات */

    --popover: 0 0% 100%;             /* نوافذ منبثقة بيضاء */
    --popover-foreground: 160 20% 15%;

    --primary: 142 76% 36%;           /* 🟢 أخضر تعليمي جميل للعناصر الأساسية */
    --primary-foreground: 0 0% 100%;  /* نص أبيض على الأخضر */
    --primary-light: 142 76% 46%;     /* أخضر فاتح للتدرجات */

    --secondary: 45 93% 47%;          /* 🟡 ذهبي تعليمي للعناصر الثانوية */
    --secondary-foreground: 0 0% 100%; /* نص أبيض على الذهبي */

    --muted: 120 25% 94%;             /* خلفية مكتومة خضراء فاتحة */
    --muted-foreground: 160 15% 45%;  /* نص مكتوم */

    --accent: 45 93% 92%;             /* ذهبي فاتح للتمييز */
    --accent-foreground: 160 20% 15%;

    --destructive: 0 84% 60%;         /* أحمر للتحذيرات */
    --destructive-foreground: 0 0% 100%;

    --border: 120 20% 88%;            /* حدود خضراء فاتحة */
    --input: 120 20% 88%;             /* حدود المدخلات */
    --ring: 142 76% 36%;              /* حلقة التركيز خضراء */

    --radius: 0.5rem;

    --sidebar-background: 0 0% 100%;  /* شريط جانبي أبيض */
    --sidebar-foreground: 160 20% 15%; /* نص داكن */
    --sidebar-primary: 142 76% 36%;   /* أخضر للعناصر المهمة */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 120 25% 94%;    /* خلفية مميزة فاتحة */
    --sidebar-accent-foreground: 160 20% 15%;
    --sidebar-border: 120 20% 88%;    /* حدود فاتحة */
    --sidebar-ring: 142 76% 36%;      /* حلقة خضراء */
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  html {
    @apply font-tajawal;
    overflow-x: hidden;
  }
  body {
    @apply bg-background text-foreground font-tajawal;
    direction: rtl;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
    font-family: 'Tajawal', 'Arial', 'Helvetica', sans-serif;
    line-height: 1.7;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Ensure light mode styles are applied */
  .light-mode {
    color-scheme: light;
  }

  /* Force background and text colors for light theme */
  .light-mode body {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Enhanced contrast for better readability */
  .light-mode .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }
}

@layer components {
  .card-hover {
    transition: all 0.3s ease-in-out;
    border-color: transparent;
  }

  .card-hover:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    border-color: hsl(var(--primary) / 0.3);
  }

  .level-card {
    background-color: hsl(var(--card));
    color: hsl(var(--card-foreground));
    text-align: right;
    padding: 1.5rem;
    border-radius: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border: 1px solid hsl(var(--muted) / 0.5);
    transition: all 0.2s ease-in-out;
  }

  .level-card:hover {
    background-color: hsl(var(--muted));
  }

  .level-heading {
    @apply text-2xl font-bold text-primary mb-4 text-center;
  }

  /* Enhanced button styles for light theme */
  .light-mode .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    transition: all 0.2s ease-in-out;
  }

  .light-mode .btn-primary:hover {
    background-color: hsl(var(--primary) / 0.9);
  }

  .light-mode .btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border));
    transition: all 0.2s ease-in-out;
  }

  .light-mode .btn-secondary:hover {
    background-color: hsl(var(--secondary) / 0.8);
  }

  /* Enhanced card styles for better contrast */
  .light-mode .card {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border) / 0.5);
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    transition: all 0.2s ease-in-out;
  }

  .light-mode .card:hover {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    border-color: hsl(var(--border));
  }

  /* RTL-enhanced components */
  .rtl-card {
    background-color: hsl(var(--card));
    color: hsl(var(--card-foreground));
    text-align: right;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border: 1px solid hsl(var(--muted) / 0.5);
    direction: rtl;
  }

  .rtl-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    direction: rtl;
  }

  .rtl-input {
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid hsl(var(--input));
    background-color: hsl(var(--background));
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    direction: rtl;
    text-align: right;
  }

  .rtl-dropdown {
    position: relative;
    direction: rtl;
  }

  .rtl-dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    width: 12rem;
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--primary) / 0.2);
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    z-index: 50;
    direction: rtl;
  }

  .rtl-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
    direction: rtl;
  }

  .rtl-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    direction: rtl;
  }

  .rtl-breadcrumb-separator {
    margin: 0 0.5rem;
    transform: scaleX(-1); /* Flip separator for RTL */
  }

  /* Light theme specific improvements */
  .light-mode {
    /* Better text contrast */
    --text-primary: 222 84% 4.9%;
    --text-secondary: 215 16% 46.9%;
    --text-muted: 215 16% 56.9%;

    /* Enhanced focus states */
    --focus-ring: 210 100% 45%;
    --focus-ring-offset: 0 0% 100%;
  }

  .light-mode input:focus,
  .light-mode textarea:focus,
  .light-mode select:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-color: hsl(var(--ring));
  }

  .light-mode .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    transition: all 0.2s ease-in-out;
  }

  /* Enhanced table styles for light theme */
  .light-mode table {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
  }

  .light-mode th {
    background-color: hsl(var(--muted) / 0.5);
    color: hsl(var(--foreground));
    font-weight: 600;
  }

  .light-mode td {
    border-top: 1px solid hsl(var(--border) / 0.5);
  }

  .light-mode tr:hover {
    background-color: hsl(var(--muted) / 0.3);
  }



  /* Mobile-specific improvements */
  .mobile-safe {
    max-width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Arabic text mobile improvements */
  @media (max-width: 768px) {
    .arabic-text {
      font-size: 16px;
      line-height: 1.9;
    }

    .arabic-paragraph {
      font-size: 16px;
      line-height: 1.9;
      margin-bottom: 1.25rem;
    }

    .arabic-heading {
      font-size: 1.5rem;
      line-height: 1.3;
      margin-bottom: 0.75rem;
    }
  }

  .mobile-dropdown {
    max-width: calc(100vw - 1rem);
    max-height: 70vh;
    overflow-y: auto;
    position: relative;
    z-index: 9999;
  }

  .mobile-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }

  /* Prevent horizontal overflow on mobile */
  @media (max-width: 768px) {
    .mobile-container {
      width: 100%;
      max-width: 100%;
      padding-left: 0.5rem;
      padding-right: 0.5rem;
      overflow-x: hidden;
    }

    .mobile-card {
      width: 100%;
      max-width: 100%;
      min-width: 0;
    }

    .mobile-button {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      min-width: 0;
      white-space: nowrap;
    }

    .mobile-image {
      width: 100%;
      height: auto;
      max-width: 100%;
      max-height: 60vh;
      object-fit: contain;
    }
  }
}
